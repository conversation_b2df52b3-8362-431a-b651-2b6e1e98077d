import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import session from 'express-session';
import cookieParser from 'cookie-parser';

import apiRoutes from './routes/apiRoutes';
import { MemStorage } from './storage/MemStorage';
import { sendContactInquiryNotifications } from './server/email';
import { checkCookieConsent } from './middleware/cookieConsent';
import { analyticsMiddleware, requestProfilingMiddleware } from './middleware/analytics';

// Extend session data interface
declare module 'express-session' {
  interface SessionData {
    userId: number;
    isAdmin: boolean;
    username: string;
    role?: string;
  }
}

export const storage = new MemStorage();

const app = express();
const port = process.env.PORT || 3001;

const allowedOrigins = [
  'http://localhost:5173',
  'http://localhost:5174',
  'https://metanord.eu',
  'https://www.metanord.eu',
  'https://metanord.vercel.app',
  'https://metanord-frontend.vercel.app',
];

function isAllowedOrigin(origin?: string) {
  if (!origin) return true;
  if (allowedOrigins.includes(origin)) return true;
  try {
    const { hostname } = new URL(origin);
    if (hostname.endsWith('.vercel.app')) return true;
  } catch (e) {
    return false;
  }
  return false;
}

// Enhanced CORS configuration for cross-origin cookie support
app.use(cors({
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean | string) => void) => {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[CORS] Request from origin: ${origin || 'undefined'}`);
    }
    if (isAllowedOrigin(origin)) {
      // Return the specific origin for proper cross-origin cookie support
      callback(null, origin || true);
    } else {
      if (process.env.NODE_ENV !== 'production') {
        console.warn(`[CORS] Blocked origin: ${origin}`);
      }
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true, // Essential for cross-origin cookies
  optionsSuccessStatus: 200, // Support legacy browsers
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'cache-control'
  ],
  exposedHeaders: ['Set-Cookie'],
}));

// Trust proxy for proper IP handling and secure cookies
app.set('trust proxy', 1);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Cookie parser middleware (must be before session)
app.use(cookieParser());

// Session configuration for production and development
if (process.env.NODE_ENV !== 'production') {
  console.log('[Session] Configuring session middleware...');
}

// Global session middleware - always applied for proper session handling
app.use(session({
  secret: process.env.SESSION_SECRET || 'metanord-admin-secret-key',
  name: 'connect.sid', // Standard session cookie name
  resave: false,
  saveUninitialized: false,
  rolling: true, // Refresh session on each request
  cookie: {
    httpOnly: true,
    // Use secure cookies in production (HTTPS required for cross-origin)
    secure: process.env.NODE_ENV === 'production',
    // Use 'none' for cross-origin requests in production, 'lax' for development
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    domain: undefined, // No domain restriction for cross-origin compatibility
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    path: '/',
  }
}));

// Session debugging middleware for development
if (process.env.NODE_ENV !== 'production') {
  app.use((req, res, next) => {
    if (req.path.startsWith('/api/admin')) {
      console.log(`[Session Debug] ${req.method} ${req.path}`);
      console.log(`[Session Debug] Session ID: ${req.sessionID || 'none'}`);
      console.log(`[Session Debug] User: ${req.session.userId ? req.session.username : 'Not logged in'}`);
      console.log(`[Session Debug] Cookies:`, Object.keys(req.cookies || {}));
    }
    next();
  });
}

// Optimized middleware for public API routes
// Skip heavy processing for public API endpoints to improve performance
const isPublicAPIRoute = (path: string): boolean => {
  // Public API routes that should skip middleware processing
  const publicRoutes = [
    '/api/products',
    '/api/projects',
    '/api/careers',
    '/api/quote',
    '/api/consent',
    '/api/cookie-policy',
    '/api/applications',
    '/health'
  ];

  return publicRoutes.some(route => path.startsWith(route)) || path === '/health';
};

// Additional optimization: Skip session processing for public API routes
const isPublicRoute = (path: string): boolean => {
  return isPublicAPIRoute(path) || path.startsWith('/api/contact');
};

// Cookie consent enforcement middleware (must be after cookie parser and session)
// Optimized to skip heavy processing for public routes
app.use((req, res, next) => {
  if (isPublicAPIRoute(req.path)) {
    // For public API routes, set minimal consent defaults and skip processing
    req.consent = {
      analytics: false,
      marketing: false,
      functional: true,
    };
    req.hasConsent = false;
    return next();
  }
  // For non-public routes, use full consent checking
  return checkCookieConsent(req, res, next);
});

// Analytics middleware that respects consent (must be after consent middleware)
// Optimized to skip for public API routes
app.use((req, res, next) => {
  if (isPublicAPIRoute(req.path)) {
    return next(); // Skip analytics for public routes
  }
  return analyticsMiddleware(req, res, next);
});

app.use((req, res, next) => {
  if (isPublicAPIRoute(req.path)) {
    return next(); // Skip profiling for public routes
  }
  return requestProfilingMiddleware(req, res, next);
});

// Основные API маршруты
app.use('/api', apiRoutes);

// Contact form handling is now managed by /api/inquiries route in routes/public/inquiries.ts

// Health check
app.get('/health', (_, res) => {
  res.json({ status: 'ok' });
});

// Debug session endpoint
app.get('/api/debug/session', (req, res) => {
  res.json({
    session: req.session,
    sessionID: req.sessionID,
    cookies: req.headers.cookie
  });
});

// Запуск сервера
app.listen(port, () => {
  console.log(`✅ MetaNord backend running on port ${port}`);
});
