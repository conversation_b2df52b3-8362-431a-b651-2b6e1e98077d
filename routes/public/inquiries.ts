import { Router, Request, Response } from 'express';
import { storage } from '../../storage';
import { insertContactInquirySchema } from '../../shared/schema';
import { sendContactInquiryNotifications } from '../../server/email';

const router = Router();

/**
 * POST /api/inquiries
 * Public endpoint for contact form submissions
 * Saves inquiry to storage AND sends email notifications
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    console.log('[Contact] New inquiry submission received:', {
      name: req.body.name,
      email: req.body.email,
      company: req.body.company || 'Not provided',
      hasPhone: !!req.body.phone
    });

    // Validate the incoming data using the schema
    const validationResult = insertContactInquirySchema.safeParse(req.body);
    
    if (!validationResult.success) {
      console.warn('[Contact] Validation failed:', validationResult.error.errors);
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationResult.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message
        }))
      });
    }

    const inquiryData = validationResult.data;

    // Add default values for required fields
    const completeInquiryData = {
      ...inquiryData,
      status: 'new',
      archived: false,
      createdAt: new Date()
    };

    console.log('[Contact] Data validated successfully, saving to storage...');

    // Save the inquiry to storage
    const savedInquiry = await storage.createContactInquiry(completeInquiryData);
    
    console.log(`[Contact] ✅ Inquiry saved successfully with ID: ${savedInquiry.id}`);

    // Verify the inquiry was saved by checking total count
    const allInquiries = await storage.getAllContactInquiries();
    console.log(`[Contact] Total inquiries in storage: ${allInquiries.length}`);

    // Send email notifications
    let emailSent = false;
    let emailError = null;

    try {
      await sendContactInquiryNotifications(savedInquiry);
      emailSent = true;
      console.log('[Contact] ✅ Email notifications sent successfully');
    } catch (error) {
      emailError = error;
      console.error('[Contact] ❌ Failed to send email notifications:', error);
      // Continue processing even if email fails - inquiry is already saved
    }

    // Log analytics data if consent is given
    if (req.consent?.analytics) {
      console.log('[Contact] Analytics: Form submission from', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        hasCompany: !!inquiryData.company,
        hasPhone: !!inquiryData.phone,
      });
    }

    // Return success response
    res.status(201).json({
      success: true,
      message: 'Inquiry submitted successfully',
      data: {
        id: savedInquiry.id,
        status: savedInquiry.status,
        submittedAt: savedInquiry.createdAt,
        emailSent,
        ...(emailError && { emailError: 'Failed to send email notification' })
      }
    });

  } catch (error) {
    console.error('[Contact] Server error processing inquiry:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit inquiry',
      error: 'Internal server error'
    });
  }
});

export default router;
