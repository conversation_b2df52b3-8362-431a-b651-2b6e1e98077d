import { Router } from 'express';
import authRoutes from './admin/auth';
import productRoutes from './admin/products';
import publicProductRoutes from './public/products';
import publicCareersRoutes from './public/careers';
import publicApplicationsRoutes from './public/applications';
import publicProjectsRoutes from './public/projects';
import publicQuoteRoutes from './public/quote';
import publicInquiryRoutes from './public/inquiries';
import publicConsentRoutes from './public/consent';
import publicCookiePolicyRoutes from './public/cookiePolicy';
import pageRoutes from './admin/pages';
import inquiryRoutes from './admin/inquiries';
import crmRoutes from './admin/crm';
import documentRoutes from './admin/documents';
import quoteRoutes from './admin/quotes';
import offerRoutes from './admin/offers';
import notificationRoutes from './admin/notifications';
import pageBuilderRoutes from './admin/pageBuilder';
import pagesManagementRoutes from './admin/pagesManagement';
import componentRoutes from './admin/components';
import projectRoutes from './admin/projects';
import jobPostingRoutes from './admin/jobPostings';
import seoRoutes from './admin/seo';
import dashboardRoutes from './admin/dashboard';
import userRoutes from './admin/users';
import contentRoutes from './admin/content';
import adminConsentRoutes from './admin/consent';

const router = Router();

// Публичные маршруты
router.use('/products', publicProductRoutes); // Публичный доступ к продуктам
router.use('/careers', publicCareersRoutes); // Публичный доступ к вакансиям
router.use('/projects', publicProjectsRoutes); // Публичный доступ к проектам
router.use('/applications', publicApplicationsRoutes); // Подача заявок на работу
router.use('/quote', publicQuoteRoutes); // Публичные запросы на расценки
router.use('/inquiries', publicInquiryRoutes); // Публичные обращения через форму контактов
router.use('/contact', publicInquiryRoutes); // Backward compatibility for /api/contact
router.use('/consent', publicConsentRoutes); // Cookie consent management
router.use('/cookie-policy', publicCookiePolicyRoutes); // Cookie policy content

// Админские маршруты
router.use('/admin', authRoutes);
router.use('/admin/products', productRoutes);
router.use('/admin/inquiries', inquiryRoutes);
router.use('/admin/crm', crmRoutes);
router.use('/admin/documents', documentRoutes);
router.use('/admin/quotes', quoteRoutes);
router.use('/admin/offers', offerRoutes);
router.use('/admin/notifications', notificationRoutes);
router.use('/admin/page-builder', pageBuilderRoutes);
router.use('/admin/pages', pagesManagementRoutes);
router.use('/admin/components', componentRoutes);
router.use('/admin/projects', projectRoutes);
router.use('/projects', projectRoutes);
router.use('/admin/job-postings', jobPostingRoutes);
router.use('/admin/careers', jobPostingRoutes); // Frontend expects /careers endpoint
router.use('/admin/seo', seoRoutes);
router.use('/admin/dashboard', dashboardRoutes);
router.use('/admin/users', userRoutes);
router.use('/admin/consent', adminConsentRoutes);
router.use('/admin/content', contentRoutes); // Make content routes more specific

export default router;
