import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { type SiteContent, type InsertSiteContent } from '../shared/schema';

interface Product {
  productId: string;
  language: string;
  category?: string;
  featured?: boolean;
  projectDate?: Date | string | null;
  productTags?: string[];
  images?: string[];
  published?: boolean;
  [key: string]: any;
}

interface Project {
  id: number;
  language: string;
  category?: string;
  featured?: boolean;
  projectDate?: Date | string | null;
  productTags?: string[];
  images?: string[];
  published?: boolean;
  [key: string]: any;
}

interface JobPosting {
  id: string;
  slug: string;
  title: Record<string, string>;
  description: Record<string, string>;
  location: Record<string, string>;
  language: string;
  published: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface ConsentRecord {
  id: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  consent: {
    analytics: boolean;
    marketing: boolean;
    functional?: boolean;
  };
  sessionId?: string;
}

interface CookiePolicyContent {
  id: number;
  language: string;
  title: string;
  content: string;
  lastUpdated: Date;
}

export class MemStorage {
  private products = new Map<string, Product>();
  private projects = new Map<string, Project>();
  private jobPostings = new Map<string, JobPosting>();
  private siteContent = new Map<string, SiteContent>();
  private siteContentCurrentId = 1;
  private projectCurrentId = 1;
  private jobPostingCurrentId = 1;

  // Cookie consent and GDPR compliance data stores
  private consentRecords = new Map<string, ConsentRecord>();
  private cookiePolicyContent = new Map<string, CookiePolicyContent>();
  private cookiePolicyCurrentId = 1;

  // Admin functionality data stores
  private users = new Map<number, any>();
  private contactInquiries = new Map<number, any>();
  private quoteRequests = new Map<number, any>();
  private crmClients = new Map<number, any>();
  private offers = new Map<number, any>();
  private documents = new Map<number, any>();
  private notifications = new Map<number, any>();
  private auditLogs = new Map<number, any>();

  // Page management data stores
  private customPages = new Map<number, any>();
  private pageTemplates = new Map<number, any>();
  private pageRevisions = new Map<number, any>();

  constructor() {
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);

    // === PRODUCTS ===
    try {
      const productsPath = path.join(__dirname, '../data/products.json');
      const raw = fs.readFileSync(productsPath, 'utf-8');
      const parsed: Product[] = JSON.parse(raw);
      let count = 0;
      for (const product of parsed) {
        if (!product?.productId || typeof product.language !== 'string') {
          console.warn(`[Storage] ⚠️ Skipped invalid product:`, product);
          continue;
        }
        if (product.projectDate)
          product.projectDate = product.projectDate ? new Date(product.projectDate as any) : null;
        if (product.createdAt)
          product.createdAt = product.createdAt ? new Date(product.createdAt as any) : null;
        if (product.updatedAt)
          product.updatedAt = product.updatedAt ? new Date(product.updatedAt as any) : null;
        const key = this.generateKey(product.productId, product.language);
        this.products.set(key, product);
        if (!this.products.has(key)) {
          console.warn(`[Storage] ⚠️ Failed to store product:`, product);
        } else {
          count++;
        }
      }
      console.log(`[Storage] ✅ Loaded ${count} products`);
    } catch (err) {
      console.error(`[Storage] ❌ Failed to load products.json`, err);
    }

    // === PROJECTS ===
    try {
      const projectsPath = path.join(__dirname, '../data/projects.json');
      const raw = fs.readFileSync(projectsPath, 'utf-8');
      const parsed: Project[] = JSON.parse(raw);
      let count = 0;
      let maxId = 0;
      for (const project of parsed) {
        if (!project.id || !project.language) continue;
        if (project.projectDate)
          project.projectDate = project.projectDate ? new Date(project.projectDate as any) : null;
        if (project.createdAt)
          project.createdAt = project.createdAt ? new Date(project.createdAt as any) : null;
        if (project.updatedAt)
          project.updatedAt = project.updatedAt ? new Date(project.updatedAt as any) : null;
        const key = this.generateKey(project.id.toString(), project.language);
        this.projects.set(key, project);
        maxId = Math.max(maxId, project.id);
        count++;
      }
      this.projectCurrentId = maxId + 1;
      console.log(`[Storage] ✅ Loaded ${count} projects, next ID: ${this.projectCurrentId}`);
    } catch (err) {
      console.error(`[Storage] ❌ Failed to load projects.json`, err);
    }

    // === SITE CONTENT ===
    try {
      const contentPath = path.join(__dirname, '../data/content.json');
      const raw = fs.readFileSync(contentPath, 'utf-8');
      const parsed: SiteContent[] = JSON.parse(raw);
      let count = 0;
      for (const item of parsed) {
        if (!item.section || !item.key || !item.language) continue;
        const key = this.buildContentKey(item.section, item.key, item.language);
        this.siteContent.set(key, item);
        if (item.id && item.id >= this.siteContentCurrentId) {
          this.siteContentCurrentId = item.id + 1;
        }
        count++;
      }
      console.log(`[Storage] ✅ Loaded ${count} content items`);
    } catch (err) {
      console.error(`[Storage] ❌ Failed to load content.json`, err);
    }

    // === JOB POSTINGS ===
    try {
      const jobPostingsPath = path.join(__dirname, '../data/job_postings.json');
      if (fs.existsSync(jobPostingsPath)) {
        const raw = fs.readFileSync(jobPostingsPath, 'utf-8');
        const parsed: JobPosting[] = JSON.parse(raw);
        let count = 0;
        let maxId = 0;
        for (const jobPosting of parsed) {
          if (!jobPosting.id || !jobPosting.slug) continue;
          if (jobPosting.createdAt)
            jobPosting.createdAt = new Date(jobPosting.createdAt as any);
          if (jobPosting.updatedAt)
            jobPosting.updatedAt = new Date(jobPosting.updatedAt as any);
          this.jobPostings.set(jobPosting.id, jobPosting);
          const numericId = parseInt(jobPosting.id);
          if (!isNaN(numericId)) {
            maxId = Math.max(maxId, numericId);
          }
          count++;
        }
        this.jobPostingCurrentId = maxId + 1;
        console.log(`[Storage] ✅ Loaded ${count} job postings, next ID: ${this.jobPostingCurrentId}`);
      } else {
        console.log(`[Storage] ℹ️ No job postings file found, starting fresh`);
      }
    } catch (err) {
      console.error(`[Storage] ❌ Failed to load job_postings.json`, err);
    }

    // Initialize admin data stores
    this.users = new Map();
    this.contactInquiries = new Map();
    this.quoteRequests = new Map();
    this.crmClients = new Map();
    this.offers = new Map();
    this.documents = new Map();
    this.notifications = new Map();
    this.auditLogs = new Map();

    // Initialize page management data stores
    this.customPages = new Map();
    this.pageTemplates = new Map();
    this.pageRevisions = new Map();

    // Initialize consent and cookie policy data stores
    this.consentRecords = new Map();
    this.cookiePolicyContent = new Map();

    // Add default admin user
    this.users.set(1, {
      id: 1,
      username: 'admin',
      password: 'admin123',
      fullName: 'Administrator',
      email: '<EMAIL>',
      role: 'admin',
      isAdmin: true,
      createdAt: new Date(),
      lastLogin: null
    });

    // Add test contact inquiries
    this.contactInquiries.set(1, {
      id: 1,
      name: "Test Customer",
      email: "<EMAIL>",
      message: "I would like to know more about your aluminum profiles. Do you ship to Finland?",
      status: "new",
      archived: false,
      company: "Test Company Ltd",
      phone: "+*********** 789",
      createdAt: new Date('2025-05-15T17:45:12.951Z')
    });

    this.contactInquiries.set(2, {
      id: 2,
      name: "Seyhun Shikhaliyev",
      email: "<EMAIL>",
      company: "MetaNord OÜ",
      message: "Hello this is a test form submission to make sure that everything works.",
      status: "new",
      archived: false,
      createdAt: new Date('2025-05-20T19:20:34.481Z')
    });

    // Initialize default cookie policy content
    this.cookiePolicyContent.set('1', {
      id: 1,
      language: 'en',
      title: 'Cookie Policy',
      content: `
# Cookie Policy

This website uses cookies to enhance your browsing experience and provide personalized content.

## Essential Cookies
These cookies are necessary for the website to function properly and cannot be disabled:
- **Session cookies**: Used to maintain your login session and security
- **Security cookies**: Used to prevent cross-site request forgery attacks

## Analytics Cookies
These cookies help us understand how visitors interact with our website:
- **Google Analytics**: Tracks page views, user behavior, and website performance
- **Performance monitoring**: Helps us identify and fix technical issues

## Marketing Cookies
These cookies are used to deliver relevant advertisements:
- **Advertising platforms**: Used to show you relevant ads on other websites
- **Social media integration**: Enables sharing content on social platforms

## Your Choices
You can control cookie preferences through our consent banner or by adjusting your browser settings. Note that disabling certain cookies may affect website functionality.

For more information about our data practices, please see our Privacy Policy.

Last updated: ${new Date().toLocaleDateString()}
      `.trim(),
      lastUpdated: new Date()
    });

    this.cookiePolicyContent.set('2', {
      id: 2,
      language: 'et',
      title: 'Küpsiste Poliitika',
      content: `
# Küpsiste Poliitika

See veebisait kasutab küpsiseid, et parandada teie sirvimiskogemust ja pakkuda personaliseeritud sisu.

## Olulised Küpsised
Need küpsised on veebisaidi nõuetekohaseks toimimiseks vajalikud ega saa neid keelata:
- **Seansi küpsised**: Kasutatakse teie sisselogimisseansi ja turvalisuse säilitamiseks
- **Turvaküpsised**: Kasutatakse saitidevaheliste päringuvõltsimise rünnakute vältimiseks

## Analüütikaküpsised
Need küpsised aitavad meil mõista, kuidas külastajad meie veebisaidiga suhtlevad:
- **Google Analytics**: Jälgib lehekülgede vaatamisi, kasutajate käitumist ja veebisaidi jõudlust
- **Jõudluse jälgimine**: Aitab meil tuvastada ja lahendada tehnilisi probleeme

## Turundusküpsised
Neid küpsiseid kasutatakse asjakohase reklaami edastamiseks:
- **Reklaamiplatvormid**: Kasutatakse teile asjakohase reklaami näitamiseks teistes veebisaitides
- **Sotsiaalmeedia integratsioon**: Võimaldab sisu jagamist sotsiaalplatvormidel

## Teie Valikud
Saate küpsiste eelistusi kontrollida meie nõusolekubänneri kaudu või oma brauseri seadete kohandamisega. Pange tähele, et teatud küpsiste keelamine võib mõjutada veebisaidi funktsionaalsust.

Lisateabe saamiseks meie andmete käitlemise kohta vaadake meie Privaatsuspoliitikat.

Viimati uuendatud: ${new Date().toLocaleDateString()}
      `.trim(),
      lastUpdated: new Date()
    });

    this.cookiePolicyCurrentId = 3;
  }

  private generateKey(id: string | number, lang: string) {
    return `${id}:${lang}`;
  }

  private buildContentKey(section: string, key: string, lang: string) {
    return `${section}:${key}:${lang}`;
  }

  async getAllProducts(language = 'en', category?: string) {
    console.log(`[getAllProducts] 🔍 Filtering products - Language: "${language}", Category: "${category || 'all'}"`);

    // Get all products from storage
    const allProducts = [...this.products.values()];

    // Step 1: Filter by publication status
    const publishedProducts = allProducts.filter(p => {
      const isPublished = p.published !== false; // Include true, undefined, null - exclude only explicit false
      return isPublished;
    });

    // Step 2: Filter by category if specified
    let categoryFilteredProducts = publishedProducts;
    if (category && category !== 'all' && category.trim() !== '') {
      categoryFilteredProducts = publishedProducts.filter(p => {
        const categoryMatch = p.category === category;
        return categoryMatch;
      });
    }

    // Step 3: Filter by language with fallback hierarchy
    const requestedLanguage = language || 'en';
    const languageFilteredProducts = categoryFilteredProducts.filter(p => {
      // If product has no language, include it as a fallback
      if (!p.language) {
        return true;
      }

      // Language priority order:
      // 1. Exact match (e.g., 'en-US' === 'en-US')
      // 2. Language code match (e.g., 'en-US' matches 'en')
      // 3. Default English fallback
      const exactMatch = p.language === requestedLanguage;
      const codeMatch = p.language === requestedLanguage.split('-')[0];
      const englishFallback = p.language === 'en';

      return exactMatch || codeMatch || englishFallback;
    });

    // Final result
    const finalResult = languageFilteredProducts;
    console.log(`[getAllProducts] ✅ Found ${finalResult.length} products (${allProducts.length} total → ${publishedProducts.length} published → ${categoryFilteredProducts.length} category → ${finalResult.length} language)`);

    return finalResult;
  }

  async getProduct(idOrProductId: string, language: string = 'en') {
    console.log("🧪 [getProduct] Requested:", { idOrProductId, language });

    const allVersions = [...this.products.values()].filter(p => {
      const match = (
        p.productId === idOrProductId ||
        (p.id !== undefined && p.id !== null && p.id.toString() === idOrProductId)
      );
      if (match) {
        console.log("✅ Match candidate:", { id: p.id, productId: p.productId, lang: p.language });
      }
      return match;
    });

    if (allVersions.length === 0) {
      console.log("❌ No products found with ID or productId:", idOrProductId);
      return undefined;
    }

    let match = allVersions.find(p => p.language === language);
    if (match) return match;

    const shortLang = language.split('-')[0];
    match = allVersions.find(p => p.language === shortLang);
    if (match) return match;

    match = allVersions.find(p => p.language === 'en');
    if (match) return match;

    return allVersions[0];
  }

  async getProductBySlug(slug: string, language: string = 'en') {
    console.log('[getProductBySlug]', slug, language);

    const direct = this.products.get(this.generateKey(slug, language));
    if (direct) return direct;

    const all = [...this.products.values()].filter(p => p && typeof p.language === 'string');

    const exact = all.find(p => p.productId === slug && p.language === language);
    if (exact) return exact;

    const shortLang = language.split('-')[0];
    const shortLangFallback = all.find(p => p.productId === slug && p.language === shortLang);
    if (shortLangFallback) return shortLangFallback;

    const englishFallback = all.find(p => p.productId === slug && p.language === 'en');
    if (englishFallback) return englishFallback;

    console.warn(`[getProductBySlug] ❌ No product found for slug: ${slug}`);
    return undefined;
  }

  async getAllProjects(filters?: {
    published?: boolean;
    language?: string;
    productTag?: string;
    year?: number;
    category?: string;
  }) {
    let projects = [...this.projects.values()];

    if (filters) {
      if (filters.published !== undefined) {
        projects = projects.filter(p => p.published === filters.published);
      }

      if (filters.language) {
        projects = projects.filter(p => p.language === filters.language);
      }

      if (filters.productTag) {
        projects = projects.filter(p =>
          p.productTags && p.productTags.includes(filters.productTag!)
        );
      }

      if (filters.year) {
        projects = projects.filter(p => p.year === filters.year);
      }

      if (filters.category && filters.category !== 'all') {
        projects = projects.filter(p => p.category === filters.category);
      }
    }

    // Sort by most recent projects first
    return projects.sort((a, b) => {
      // First sort by featured status
      if (a.featured && !b.featured) return -1;
      if (!a.featured && b.featured) return 1;

      // Then sort by date (desc)
      const dateA = a.projectDate || a.createdAt;
      const dateB = b.projectDate || b.createdAt;
      if (dateA && dateB) {
        return new Date(dateB).getTime() - new Date(dateA).getTime();
      }
      return 0;
    });
  }

  async getProject(id: number): Promise<Project | undefined> {
    // Find project by ID across all language versions
    for (const project of this.projects.values()) {
      if (project.id === id) {
        return project;
      }
    }
    return undefined;
  }

  async createProject(projectData: any): Promise<Project> {
    const newId = this.projectCurrentId++;

    const newProject: Project = {
      ...projectData,
      id: newId,
      language: projectData.language || 'en',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const key = this.generateKey(newId.toString(), newProject.language);
    this.projects.set(key, newProject);

    return newProject;
  }

  async updateProject(id: number, updates: any): Promise<Project | undefined> {
    // Find the existing project
    const existingProject = await this.getProject(id);
    if (!existingProject) {
      return undefined;
    }

    const updatedProject: Project = {
      ...existingProject,
      ...updates,
      id, // Ensure ID doesn't change
      updatedAt: new Date(),
    };

    // Update the project in storage
    const key = this.generateKey(id.toString(), updatedProject.language);
    this.projects.set(key, updatedProject);

    return updatedProject;
  }

  async deleteProject(id: number): Promise<boolean> {
    // Find and delete all language versions of this project
    const keysToDelete = [...this.projects.keys()].filter(k => k.startsWith(`${id}:`));

    if (keysToDelete.length === 0) {
      return false;
    }

    for (const key of keysToDelete) {
      this.projects.delete(key);
    }

    return true;
  }

  async createProduct(product: Product) {
    const key = this.generateKey(product.productId, product.language);
    this.products.set(key, product);
    return product;
  }

  async updateProduct(id: string, product: Product) {
    // First, check if the product exists
    const existingProduct = await this.getProduct(id, product.language);
    if (!existingProduct) {
      console.error(`[MemStorage.updateProduct] ❌ Product not found: id=${id}, language=${product.language}`);
      return undefined;
    }

    // Create the updated product with proper timestamps
    const updatedProduct = {
      ...existingProduct,
      ...product,
      id: existingProduct.id || existingProduct.productId, // Preserve the ID
      productId: existingProduct.productId, // Preserve the productId
      updatedAt: new Date(),
      createdAt: existingProduct.createdAt || new Date()
    };

    const key = this.generateKey(id, product.language);
    this.products.set(key, updatedProduct);

    console.log(`[MemStorage.updateProduct] ✅ Updated product: ${(updatedProduct as any).title || updatedProduct.productId} (${id}:${product.language})`);
    return updatedProduct;
  }

  async deleteProduct(id: string) {
    const keysToDelete = [...this.products.keys()].filter(k => k.startsWith(`${id}:`));
    for (const key of keysToDelete) {
      this.products.delete(key);
    }
  }

  async getAllPages(): Promise<SiteContent[]> {
    return [...this.siteContent.values()].filter(c => c.section === 'page' && c.language === 'en');
  }

  async getPageById(id: string): Promise<SiteContent | undefined> {
    const key = this.buildContentKey('page', id, 'en');
    return this.siteContent.get(key);
  }

  async savePageContent(content: InsertSiteContent): Promise<SiteContent> {
    const section = content.section ?? 'page';
    const language = content.language ?? 'en';
    const key = this.buildContentKey(section, content.key, language);
    const existing = this.siteContent.get(key);
    if (existing) {
      const updated: SiteContent = { ...existing, ...content, section, language };
      this.siteContent.set(key, updated);
      return updated;
    }

    const newContent: SiteContent = {
      ...content,
      section,
      language,
      id: this.siteContentCurrentId++,
    };
    this.siteContent.set(key, newContent);
    return newContent;
  }

  // Site content management methods
  async getAllSiteContent(language = 'en', section?: string): Promise<SiteContent[]> {
    const allContent = Array.from(this.siteContent.values());

    return allContent.filter(content => {
      const languageMatch = content.language === language;
      const sectionMatch = section ? content.section === section : true;
      return languageMatch && sectionMatch;
    });
  }

  async getSiteContent(id: number): Promise<SiteContent | undefined> {
    return Array.from(this.siteContent.values()).find(content => content.id === id);
  }

  async deleteSiteContent(id: number): Promise<boolean> {
    const content = await this.getSiteContent(id);
    if (!content) return false;

    const key = this.buildContentKey(content.section, content.key, content.language);
    return this.siteContent.delete(key);
  }

  // ===== ADMIN METHODS =====

  // Users management
  async getAllUsers(): Promise<any[]> {
    return Array.from(this.users.values());
  }

  async getUser(id: number): Promise<any | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<any | undefined> {
    return Array.from(this.users.values()).find(u => u.username === username);
  }

  async createUser(userData: any): Promise<any> {
    const id = this.users.size + 1;
    const user = { ...userData, id, createdAt: new Date() };
    this.users.set(id, user);
    return user;
  }

  async updateUser(id: number, userData: any): Promise<any | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;
    const updated = { ...user, ...userData, updatedAt: new Date() };
    this.users.set(id, updated);
    return updated;
  }

  async deleteUser(id: number): Promise<boolean> {
    return this.users.delete(id);
  }

  async verifyUser(username: string, password: string): Promise<any | null> {
    const user = await this.getUserByUsername(username);
    if (!user) return null;
    // Simple password check (in real app, use bcrypt)
    return user.password === password ? user : null;
  }

  async updateUserLastLogin(id: number): Promise<any | undefined> {
    const user = this.users.get(id);
    if (!user) return undefined;
    user.lastLogin = new Date();
    return user;
  }

  // Contact Inquiries management
  async getAllContactInquiries(): Promise<any[]> {
    return Array.from(this.contactInquiries.values());
  }

  async getContactInquiry(id: number): Promise<any | undefined> {
    return this.contactInquiries.get(id);
  }

  async createContactInquiry(inquiryData: any): Promise<any> {
    const id = this.contactInquiries.size + 1;
    const inquiry = { ...inquiryData, id, createdAt: new Date(), status: 'new', archived: false };
    this.contactInquiries.set(id, inquiry);
    return inquiry;
  }

  async updateContactInquiry(id: number, updates: any): Promise<any | undefined> {
    const inquiry = this.contactInquiries.get(id);
    if (!inquiry) return undefined;
    const updated = { ...inquiry, ...updates, updatedAt: new Date() };
    this.contactInquiries.set(id, updated);
    return updated;
  }

  async deleteContactInquiry(id: number): Promise<boolean> {
    return this.contactInquiries.delete(id);
  }

  // Quote Requests management
  async getAllQuoteRequests(filters?: any): Promise<any[]> {
    let requests = Array.from(this.quoteRequests.values());
    if (filters?.status) {
      requests = requests.filter(r => r.status === filters.status);
    }
    if (filters?.archived !== undefined) {
      requests = requests.filter(r => r.archived === filters.archived);
    }
    return requests;
  }

  async createQuoteRequest(requestData: any): Promise<any> {
    const id = this.quoteRequests.size + 1;
    const request = { ...requestData, id, createdAt: new Date() };
    this.quoteRequests.set(id, request);
    return request;
  }

  async updateQuoteRequestStatus(requestId: number, status: string): Promise<any | undefined> {
    const request = this.quoteRequests.get(requestId);
    if (!request) return undefined;
    request.status = status;
    request.updatedAt = new Date();
    return request;
  }

  async archiveQuoteRequest(requestId: number, archived: boolean): Promise<any | undefined> {
    const request = this.quoteRequests.get(requestId);
    if (!request) return undefined;
    request.archived = archived;
    request.updatedAt = new Date();
    return request;
  }

  async exportQuoteRequests(format: string, filters?: any): Promise<string> {
    const requests = await this.getAllQuoteRequests(filters);
    if (format === 'csv') {
      // Simple CSV export
      const headers = 'ID,Customer Name,Email,Status,Created At\n';
      const rows = requests.map(r => `${r.id},"${r.customerName}","${r.customerEmail}","${r.status}","${r.createdAt}"`).join('\n');
      return headers + rows;
    }
    return JSON.stringify(requests, null, 2);
  }

  // CRM management
  async getAllCrmClients(filters?: any): Promise<any[]> {
    let clients = Array.from(this.crmClients.values());
    if (filters?.name) {
      clients = clients.filter(c => c.name.toLowerCase().includes(filters.name.toLowerCase()));
    }
    if (filters?.company) {
      clients = clients.filter(c => c.company?.toLowerCase().includes(filters.company.toLowerCase()));
    }
    if (filters?.leadStatus) {
      clients = clients.filter(c => c.leadStatus === filters.leadStatus);
    }
    return clients;
  }

  async getCrmClient(id: number): Promise<any | undefined> {
    return this.crmClients.get(id);
  }

  async getCrmClientByEmail(email: string): Promise<any | undefined> {
    return Array.from(this.crmClients.values()).find(c => c.email === email);
  }

  async createCrmClient(clientData: any): Promise<any> {
    const id = this.crmClients.size + 1;
    const client = { ...clientData, id, createdAt: new Date() };
    this.crmClients.set(id, client);
    return client;
  }

  async updateCrmClient(id: number, updates: any): Promise<any | undefined> {
    const client = this.crmClients.get(id);
    if (!client) return undefined;
    const updated = { ...client, ...updates, updatedAt: new Date() };
    this.crmClients.set(id, updated);
    return updated;
  }

  async deleteCrmClient(id: number): Promise<boolean> {
    return this.crmClients.delete(id);
  }

  // Offers management
  async getAllOffers(filters?: any): Promise<any[]> {
    let offers = Array.from(this.offers.values());
    if (filters?.status) {
      offers = offers.filter(o => o.status === filters.status);
    }
    return offers;
  }

  async getOffer(id: number): Promise<any | undefined> {
    return this.offers.get(id);
  }

  async getOfferByNumber(offerNumber: string): Promise<any | undefined> {
    return Array.from(this.offers.values()).find(o => o.offerNumber === offerNumber);
  }

  async createOffer(offerData: any): Promise<any> {
    const id = this.offers.size + 1;
    const offer = { ...offerData, id, createdAt: new Date() };
    this.offers.set(id, offer);
    return offer;
  }

  async updateOffer(id: number, updates: any): Promise<any | undefined> {
    const offer = this.offers.get(id);
    if (!offer) return undefined;
    const updated = { ...offer, ...updates, updatedAt: new Date() };
    this.offers.set(id, updated);
    return updated;
  }

  async deleteOffer(id: number): Promise<boolean> {
    return this.offers.delete(id);
  }

  async getClientOffers(clientId: number): Promise<any[]> {
    return Array.from(this.offers.values()).filter(o => o.clientId === clientId);
  }

  // Documents management
  async getAllDocuments(language?: string, documentType?: string, productCategory?: string, categoryId?: number): Promise<any[]> {
    let documents = Array.from(this.documents.values());
    if (language) {
      documents = documents.filter(d => d.language === language);
    }
    if (documentType) {
      documents = documents.filter(d => d.documentType === documentType);
    }
    if (productCategory) {
      documents = documents.filter(d => d.productCategory === productCategory);
    }
    if (categoryId) {
      documents = documents.filter(d => d.categoryId === categoryId);
    }
    return documents;
  }

  async getDocument(id: number): Promise<any | undefined> {
    return this.documents.get(id);
  }

  async createDocument(documentData: any): Promise<any> {
    const id = this.documents.size + 1;
    const document = { ...documentData, id, createdAt: new Date(), downloadCount: 0 };
    this.documents.set(id, document);
    return document;
  }

  async updateDocument(id: number, updates: any): Promise<any | undefined> {
    const document = this.documents.get(id);
    if (!document) return undefined;
    const updated = { ...document, ...updates, updatedAt: new Date() };
    this.documents.set(id, updated);
    return updated;
  }

  async deleteDocument(id: number): Promise<boolean> {
    return this.documents.delete(id);
  }

  async incrementDownloadCount(id: number): Promise<any | undefined> {
    const document = this.documents.get(id);
    if (!document) return undefined;
    document.downloadCount = (document.downloadCount || 0) + 1;
    return document;
  }

  // Notifications management
  async getUserNotifications(userId: number, includeRead: boolean = false): Promise<any[]> {
    let notifications = Array.from(this.notifications.values()).filter(n => n.userId === userId);
    if (!includeRead) {
      notifications = notifications.filter(n => !n.isRead);
    }
    return notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  async createNotification(notificationData: any): Promise<any> {
    const id = this.notifications.size + 1;
    const notification = { ...notificationData, id, createdAt: new Date() };
    this.notifications.set(id, notification);
    return notification;
  }

  async markNotificationAsRead(id: number): Promise<boolean> {
    const notification = this.notifications.get(id);
    if (!notification) return false;
    notification.isRead = true;
    notification.readAt = new Date();
    return true;
  }

  async markAllNotificationsAsRead(userId: number): Promise<boolean> {
    const userNotifications = Array.from(this.notifications.values()).filter(n => n.userId === userId);
    userNotifications.forEach(n => {
      n.isRead = true;
      n.readAt = new Date();
    });
    return true;
  }

  async deleteNotification(id: number): Promise<boolean> {
    return this.notifications.delete(id);
  }

  async markNotificationAsUnread(id: number): Promise<boolean> {
    const notification = this.notifications.get(id);
    if (!notification) return false;
    notification.isRead = false;
    delete notification.readAt;
    return true;
  }

  async clearAllNotifications(userId: number): Promise<boolean> {
    const userNotifications = Array.from(this.notifications.values()).filter(n => n.userId === userId);
    userNotifications.forEach(n => {
      this.notifications.delete(n.id);
    });
    return true;
  }

  // Audit logs (for dashboard activity)
  async getAuditLogs(filters?: any): Promise<any[]> {
    let logs = Array.from(this.auditLogs.values());
    if (filters?.fromDate) {
      logs = logs.filter(l => new Date(l.timestamp) >= filters.fromDate);
    }
    return logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  // Document categories management
  private documentCategories = new Map<number, any>();
  private documentCategoryCurrentId = 1;

  async getAllDocumentCategories(): Promise<any[]> {
    return Array.from(this.documentCategories.values());
  }

  async createDocumentCategory(data: any): Promise<any> {
    const id = this.documentCategoryCurrentId++;
    const category = { ...data, id, createdAt: new Date() };
    this.documentCategories.set(id, category);
    return category;
  }

  async getDocumentCategory(id: number): Promise<any | undefined> {
    return this.documentCategories.get(id);
  }

  async updateDocumentCategory(id: number, data: any): Promise<any | undefined> {
    const category = this.documentCategories.get(id);
    if (!category) return undefined;
    const updated = { ...category, ...data, updatedAt: new Date() };
    this.documentCategories.set(id, updated);
    return updated;
  }

  async deleteDocumentCategory(id: number): Promise<boolean> {
    return this.documentCategories.delete(id);
  }

  // CRM features
  async getCrmActivityLogs(clientId: number): Promise<any[]> { return []; }
  async createCrmActivityLog(data: any): Promise<any> { return data; }
  async getCrmEmailLogs(clientId: number): Promise<any[]> { return []; }
  async createCrmEmailLog(data: any): Promise<any> { return data; }
  async getAllCrmEmailTemplates(): Promise<any[]> { return []; }

  // ===== JOB POSTINGS METHODS =====

  async getAllJobPostings(language = 'en', publishedOnly = false): Promise<JobPosting[]> {
    console.log(`[getAllJobPostings] 🔍 Filtering job postings - Language: "${language}", Published only: ${publishedOnly}`);

    let jobPostings = [...this.jobPostings.values()];

    // Filter by publication status if requested
    if (publishedOnly) {
      jobPostings = jobPostings.filter(jp => jp.published === true);
    }

    // Apply language fallback hierarchy
    const filteredJobPostings = jobPostings.filter(jp => {
      // Language priority order:
      // 1. Exact match (e.g., 'en-US' === 'en-US')
      // 2. Language code match (e.g., 'en-US' matches 'en')
      // 3. Default English fallback
      const exactMatch = jp.language === language;
      const codeMatch = jp.language === language.split('-')[0];
      const englishFallback = jp.language === 'en';

      return exactMatch || codeMatch || englishFallback;
    });

    // Sort by creation date (newest first)
    const sortedJobPostings = filteredJobPostings.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    console.log(`[getAllJobPostings] ✅ Found ${sortedJobPostings.length} job postings`);
    return sortedJobPostings;
  }

  async getJobPosting(id: string): Promise<JobPosting | undefined> {
    return this.jobPostings.get(id);
  }

  async getJobPostingBySlug(slug: string, language = 'en'): Promise<JobPosting | undefined> {
    console.log(`[getJobPostingBySlug] 🔍 Looking for slug: "${slug}", language: "${language}"`);

    const allJobPostings = [...this.jobPostings.values()];

    // Find job postings with matching slug
    const matchingJobPostings = allJobPostings.filter(jp => jp.slug === slug);

    if (matchingJobPostings.length === 0) {
      console.log(`[getJobPostingBySlug] ❌ No job posting found with slug: ${slug}`);
      return undefined;
    }

    // Apply language fallback hierarchy
    // 1. Exact match
    let match = matchingJobPostings.find(jp => jp.language === language);
    if (match) {
      console.log(`[getJobPostingBySlug] ✅ Found exact language match`);
      return match;
    }

    // 2. Language code match
    const shortLang = language.split('-')[0];
    match = matchingJobPostings.find(jp => jp.language === shortLang);
    if (match) {
      console.log(`[getJobPostingBySlug] ✅ Found language code match`);
      return match;
    }

    // 3. English fallback
    match = matchingJobPostings.find(jp => jp.language === 'en');
    if (match) {
      console.log(`[getJobPostingBySlug] ✅ Found English fallback`);
      return match;
    }

    // 4. Return first available
    console.log(`[getJobPostingBySlug] ⚠️ Using first available job posting`);
    return matchingJobPostings[0];
  }

  async createJobPosting(jobPostingData: any): Promise<JobPosting> {
    const id = (this.jobPostingCurrentId++).toString();

    const newJobPosting: JobPosting = {
      ...jobPostingData,
      id,
      language: jobPostingData.language || 'en',
      published: jobPostingData.published || false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.jobPostings.set(id, newJobPosting);
    console.log(`[createJobPosting] ✅ Created job posting with ID: ${id}`);

    return newJobPosting;
  }

  async updateJobPosting(id: string, updates: any): Promise<JobPosting | undefined> {
    const existingJobPosting = this.jobPostings.get(id);
    if (!existingJobPosting) {
      console.log(`[updateJobPosting] ❌ Job posting not found with ID: ${id}`);
      return undefined;
    }

    const updatedJobPosting: JobPosting = {
      ...existingJobPosting,
      ...updates,
      id, // Ensure ID doesn't change
      updatedAt: new Date(),
    };

    this.jobPostings.set(id, updatedJobPosting);
    console.log(`[updateJobPosting] ✅ Updated job posting with ID: ${id}`);

    return updatedJobPosting;
  }

  async deleteJobPosting(id: string): Promise<boolean> {
    const deleted = this.jobPostings.delete(id);
    if (deleted) {
      console.log(`[deleteJobPosting] ✅ Deleted job posting with ID: ${id}`);
    } else {
      console.log(`[deleteJobPosting] ❌ Job posting not found with ID: ${id}`);
    }
    return deleted;
  }

  // ===== COOKIE CONSENT & GDPR COMPLIANCE METHODS =====

  async storeConsentRecord(consentData: {
    ipAddress: string;
    userAgent: string;
    consent: { analytics: boolean; marketing: boolean; functional?: boolean };
    sessionId?: string;
  }): Promise<ConsentRecord> {
    const id = `consent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const record: ConsentRecord = {
      id,
      ...consentData,
      timestamp: new Date(),
    };

    this.consentRecords.set(id, record);
    console.log(`[storeConsentRecord] ✅ Stored consent record with ID: ${id}`);
    return record;
  }

  async getConsentRecord(id: string): Promise<ConsentRecord | undefined> {
    return this.consentRecords.get(id);
  }

  async getAllConsentRecords(filters?: {
    fromDate?: Date;
    toDate?: Date;
    ipAddress?: string;
    analytics?: boolean;
    marketing?: boolean;
  }): Promise<ConsentRecord[]> {
    let records = Array.from(this.consentRecords.values());

    if (filters) {
      if (filters.fromDate) {
        records = records.filter(r => r.timestamp >= filters.fromDate!);
      }
      if (filters.toDate) {
        records = records.filter(r => r.timestamp <= filters.toDate!);
      }
      if (filters.ipAddress) {
        records = records.filter(r => r.ipAddress === filters.ipAddress);
      }
      if (filters.analytics !== undefined) {
        records = records.filter(r => r.consent.analytics === filters.analytics);
      }
      if (filters.marketing !== undefined) {
        records = records.filter(r => r.consent.marketing === filters.marketing);
      }
    }

    return records.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  async deleteConsentRecord(id: string): Promise<boolean> {
    const deleted = this.consentRecords.delete(id);
    if (deleted) {
      console.log(`[deleteConsentRecord] ✅ Deleted consent record with ID: ${id}`);
    }
    return deleted;
  }

  // Cookie Policy Content Management
  async getCookiePolicyContent(language = 'en'): Promise<CookiePolicyContent | undefined> {
    // Apply language fallback hierarchy
    const allContent = Array.from(this.cookiePolicyContent.values());

    // 1. Exact match
    let match = allContent.find(c => c.language === language);
    if (match) return match;

    // 2. Language code match
    const shortLang = language.split('-')[0];
    match = allContent.find(c => c.language === shortLang);
    if (match) return match;

    // 3. English fallback
    match = allContent.find(c => c.language === 'en');
    if (match) return match;

    // 4. Return first available
    return allContent[0];
  }

  async saveCookiePolicyContent(content: {
    language: string;
    title: string;
    content: string;
  }): Promise<CookiePolicyContent> {
    const existing = Array.from(this.cookiePolicyContent.values())
      .find(c => c.language === content.language);

    if (existing) {
      const updated: CookiePolicyContent = {
        ...existing,
        ...content,
        lastUpdated: new Date(),
      };
      this.cookiePolicyContent.set(existing.id.toString(), updated);
      console.log(`[saveCookiePolicyContent] ✅ Updated cookie policy for language: ${content.language}`);
      return updated;
    }

    const newContent: CookiePolicyContent = {
      ...content,
      id: this.cookiePolicyCurrentId++,
      lastUpdated: new Date(),
    };
    this.cookiePolicyContent.set(newContent.id.toString(), newContent);
    console.log(`[saveCookiePolicyContent] ✅ Created cookie policy for language: ${content.language}`);
    return newContent;
  }

  async getAllCookiePolicyContent(): Promise<CookiePolicyContent[]> {
    return Array.from(this.cookiePolicyContent.values())
      .sort((a, b) => b.lastUpdated.getTime() - a.lastUpdated.getTime());
  }

  async deleteCookiePolicyContent(id: number): Promise<boolean> {
    const deleted = this.cookiePolicyContent.delete(id.toString());
    if (deleted) {
      console.log(`[deleteCookiePolicyContent] ✅ Deleted cookie policy content with ID: ${id}`);
    }
    return deleted;
  }

  // ===== PAGE ANALYTICS METHODS =====

  async getPageStatistics(range: string): Promise<any> {
    const allPages: any[] = Array.from(this.customPages.values());
    const now = new Date();
    let startDate: Date;

    // Calculate date range
    switch (range) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Filter pages by date range
    const pagesInRange = allPages.filter((page: any) =>
      page.createdAt && page.createdAt >= startDate
    );

    // Calculate statistics
    const stats = {
      total: allPages.length,
      published: allPages.filter((p: any) => p.status === 'published').length,
      drafts: allPages.filter((p: any) => p.status === 'draft').length,
      archived: allPages.filter((p: any) => p.status === 'archived').length,
      createdInRange: pagesInRange.length,
      views: this.generateMockViews(allPages), // Mock data for demo
      avgViewsPerPage: Math.round(this.generateMockViews(allPages) / Math.max(allPages.length, 1)),
      topPerformers: this.getTopPerformingPages(allPages),
      recentActivity: this.getRecentPageActivity(allPages, 10)
    };

    return stats;
  }

  async getPageAnalytics(range: string): Promise<any> {
    const allPages: any[] = Array.from(this.customPages.values());

    // Generate analytics data for each page
    const analytics = allPages.map((page: any) => ({
      id: page.id,
      title: page.title,
      slug: page.slug,
      status: page.status,
      language: page.language,
      views: this.generateMockPageViews(page.id), // Mock data for demo
      uniqueVisitors: this.generateMockUniqueVisitors(page.id),
      bounceRate: this.generateMockBounceRate(),
      avgTimeOnPage: this.generateMockTimeOnPage(),
      createdAt: page.createdAt,
      updatedAt: page.updatedAt,
      publishedAt: page.publishedAt
    }));

    // Sort by views descending
    analytics.sort((a, b) => b.views - a.views);

    return analytics;
  }

  async getPageLanguageStats(): Promise<any> {
    const allPages: any[] = Array.from(this.customPages.values());
    const languageStats: { [key: string]: number } = {};

    // Count pages by language
    allPages.forEach((page: any) => {
      const lang = page.language || 'en';
      languageStats[lang] = (languageStats[lang] || 0) + 1;
    });

    // Convert to array format with percentages
    const total = allPages.length;
    const stats = Object.entries(languageStats).map(([language, count]) => ({
      language,
      count,
      percentage: total > 0 ? Math.round((count / total) * 100) : 0
    }));

    // Sort by count descending
    stats.sort((a, b) => b.count - a.count);

    return {
      total,
      languages: stats,
      distribution: languageStats
    };
  }

  // Helper methods for generating mock analytics data
  private generateMockViews(pages: any[]): number {
    return pages.reduce((total: number, page: any) => total + this.generateMockPageViews(page.id), 0);
  }

  private generateMockPageViews(pageId: number): number {
    // Generate consistent mock data based on page ID
    const seed = pageId * 123;
    return Math.floor((seed % 1000) + 50);
  }

  private generateMockUniqueVisitors(pageId: number): number {
    const views = this.generateMockPageViews(pageId);
    return Math.floor(views * 0.7); // Assume 70% unique visitors
  }

  private generateMockBounceRate(): number {
    return Math.floor(Math.random() * 40) + 30; // 30-70%
  }

  private generateMockTimeOnPage(): number {
    return Math.floor(Math.random() * 180) + 60; // 60-240 seconds
  }

  private getTopPerformingPages(pages: any[]): any[] {
    return pages
      .map((page: any) => ({
        id: page.id,
        title: page.title,
        slug: page.slug,
        views: this.generateMockPageViews(page.id),
        status: page.status
      }))
      .sort((a, b) => b.views - a.views)
      .slice(0, 5);
  }

  private getRecentPageActivity(pages: any[], limit: number): any[] {
    return pages
      .filter((page: any) => page.updatedAt)
      .sort((a: any, b: any) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, limit)
      .map((page: any) => ({
        id: page.id,
        title: page.title,
        action: page.status === 'published' ? 'published' : 'updated',
        timestamp: page.updatedAt,
        status: page.status
      }));
  }
}
