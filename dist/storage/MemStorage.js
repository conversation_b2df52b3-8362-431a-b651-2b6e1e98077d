import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
export class MemStorage {
    constructor() {
        this.products = new Map();
        this.projects = new Map();
        this.jobPostings = new Map();
        this.siteContent = new Map();
        this.siteContentCurrentId = 1;
        this.projectCurrentId = 1;
        this.jobPostingCurrentId = 1;
        // Cookie consent and GDPR compliance data stores
        this.consentRecords = new Map();
        this.cookiePolicyContent = new Map();
        this.cookiePolicyCurrentId = 1;
        // Admin functionality data stores
        this.users = new Map();
        this.contactInquiries = new Map();
        this.quoteRequests = new Map();
        this.crmClients = new Map();
        this.offers = new Map();
        this.documents = new Map();
        this.notifications = new Map();
        this.auditLogs = new Map();
        // Page management data stores
        this.customPages = new Map();
        this.pageTemplates = new Map();
        this.pageRevisions = new Map();
        // Document categories management
        this.documentCategories = new Map();
        this.documentCategoryCurrentId = 1;
        const __filename = fileURLToPath(import.meta.url);
        const __dirname = path.dirname(__filename);
        // === PRODUCTS ===
        try {
            const productsPath = path.join(__dirname, '../data/products.json');
            const raw = fs.readFileSync(productsPath, 'utf-8');
            const parsed = JSON.parse(raw);
            let count = 0;
            for (const product of parsed) {
                if (!product?.productId || typeof product.language !== 'string') {
                    console.warn(`[Storage] ⚠️ Skipped invalid product:`, product);
                    continue;
                }
                if (product.projectDate)
                    product.projectDate = product.projectDate ? new Date(product.projectDate) : null;
                if (product.createdAt)
                    product.createdAt = product.createdAt ? new Date(product.createdAt) : null;
                if (product.updatedAt)
                    product.updatedAt = product.updatedAt ? new Date(product.updatedAt) : null;
                const key = this.generateKey(product.productId, product.language);
                this.products.set(key, product);
                if (!this.products.has(key)) {
                    console.warn(`[Storage] ⚠️ Failed to store product:`, product);
                }
                else {
                    count++;
                }
            }
            console.log(`[Storage] ✅ Loaded ${count} products`);
        }
        catch (err) {
            console.error(`[Storage] ❌ Failed to load products.json`, err);
        }
        // === PROJECTS ===
        try {
            const projectsPath = path.join(__dirname, '../data/projects.json');
            const raw = fs.readFileSync(projectsPath, 'utf-8');
            const parsed = JSON.parse(raw);
            let count = 0;
            let maxId = 0;
            for (const project of parsed) {
                if (!project.id || !project.language)
                    continue;
                if (project.projectDate)
                    project.projectDate = project.projectDate ? new Date(project.projectDate) : null;
                if (project.createdAt)
                    project.createdAt = project.createdAt ? new Date(project.createdAt) : null;
                if (project.updatedAt)
                    project.updatedAt = project.updatedAt ? new Date(project.updatedAt) : null;
                const key = this.generateKey(project.id.toString(), project.language);
                this.projects.set(key, project);
                maxId = Math.max(maxId, project.id);
                count++;
            }
            this.projectCurrentId = maxId + 1;
            console.log(`[Storage] ✅ Loaded ${count} projects, next ID: ${this.projectCurrentId}`);
        }
        catch (err) {
            console.error(`[Storage] ❌ Failed to load projects.json`, err);
        }
        // === SITE CONTENT ===
        try {
            const contentPath = path.join(__dirname, '../data/content.json');
            const raw = fs.readFileSync(contentPath, 'utf-8');
            const parsed = JSON.parse(raw);
            let count = 0;
            for (const item of parsed) {
                if (!item.section || !item.key || !item.language)
                    continue;
                const key = this.buildContentKey(item.section, item.key, item.language);
                this.siteContent.set(key, item);
                if (item.id && item.id >= this.siteContentCurrentId) {
                    this.siteContentCurrentId = item.id + 1;
                }
                count++;
            }
            console.log(`[Storage] ✅ Loaded ${count} content items`);
        }
        catch (err) {
            console.error(`[Storage] ❌ Failed to load content.json`, err);
        }
        // === JOB POSTINGS ===
        try {
            const jobPostingsPath = path.join(__dirname, '../data/job_postings.json');
            if (fs.existsSync(jobPostingsPath)) {
                const raw = fs.readFileSync(jobPostingsPath, 'utf-8');
                const parsed = JSON.parse(raw);
                let count = 0;
                let maxId = 0;
                for (const jobPosting of parsed) {
                    if (!jobPosting.id || !jobPosting.slug)
                        continue;
                    if (jobPosting.createdAt)
                        jobPosting.createdAt = new Date(jobPosting.createdAt);
                    if (jobPosting.updatedAt)
                        jobPosting.updatedAt = new Date(jobPosting.updatedAt);
                    this.jobPostings.set(jobPosting.id, jobPosting);
                    const numericId = parseInt(jobPosting.id);
                    if (!isNaN(numericId)) {
                        maxId = Math.max(maxId, numericId);
                    }
                    count++;
                }
                this.jobPostingCurrentId = maxId + 1;
                console.log(`[Storage] ✅ Loaded ${count} job postings, next ID: ${this.jobPostingCurrentId}`);
            }
            else {
                console.log(`[Storage] ℹ️ No job postings file found, starting fresh`);
            }
        }
        catch (err) {
            console.error(`[Storage] ❌ Failed to load job_postings.json`, err);
        }
        // Initialize admin data stores
        this.users = new Map();
        this.contactInquiries = new Map();
        this.quoteRequests = new Map();
        this.crmClients = new Map();
        this.offers = new Map();
        this.documents = new Map();
        this.notifications = new Map();
        this.auditLogs = new Map();
        // Initialize page management data stores
        this.customPages = new Map();
        this.pageTemplates = new Map();
        this.pageRevisions = new Map();
        // Initialize consent and cookie policy data stores
        this.consentRecords = new Map();
        this.cookiePolicyContent = new Map();
        // Add default admin user
        this.users.set(1, {
            id: 1,
            username: 'admin',
            password: 'admin123',
            fullName: 'Administrator',
            email: '<EMAIL>',
            role: 'admin',
            isAdmin: true,
            createdAt: new Date(),
            lastLogin: null
        });
        // Add test contact inquiries
        this.contactInquiries.set(1, {
            id: 1,
            name: "Test Customer",
            email: "<EMAIL>",
            message: "I would like to know more about your aluminum profiles. Do you ship to Finland?",
            status: "new",
            archived: false,
            company: "Test Company Ltd",
            phone: "+*********** 789",
            createdAt: new Date('2025-05-15T17:45:12.951Z')
        });
        this.contactInquiries.set(2, {
            id: 2,
            name: "Seyhun Shikhaliyev",
            email: "<EMAIL>",
            company: "MetaNord OÜ",
            message: "Hello this is a test form submission to make sure that everything works.",
            status: "new",
            archived: false,
            createdAt: new Date('2025-05-20T19:20:34.481Z')
        });
        // Initialize default cookie policy content
        this.cookiePolicyContent.set('1', {
            id: 1,
            language: 'en',
            title: 'Cookie Policy',
            content: `
# Cookie Policy

This website uses cookies to enhance your browsing experience and provide personalized content.

## Essential Cookies
These cookies are necessary for the website to function properly and cannot be disabled:
- **Session cookies**: Used to maintain your login session and security
- **Security cookies**: Used to prevent cross-site request forgery attacks

## Analytics Cookies
These cookies help us understand how visitors interact with our website:
- **Google Analytics**: Tracks page views, user behavior, and website performance
- **Performance monitoring**: Helps us identify and fix technical issues

## Marketing Cookies
These cookies are used to deliver relevant advertisements:
- **Advertising platforms**: Used to show you relevant ads on other websites
- **Social media integration**: Enables sharing content on social platforms

## Your Choices
You can control cookie preferences through our consent banner or by adjusting your browser settings. Note that disabling certain cookies may affect website functionality.

For more information about our data practices, please see our Privacy Policy.

Last updated: ${new Date().toLocaleDateString()}
      `.trim(),
            lastUpdated: new Date()
        });
        this.cookiePolicyContent.set('2', {
            id: 2,
            language: 'et',
            title: 'Küpsiste Poliitika',
            content: `
# Küpsiste Poliitika

See veebisait kasutab küpsiseid, et parandada teie sirvimiskogemust ja pakkuda personaliseeritud sisu.

## Olulised Küpsised
Need küpsised on veebisaidi nõuetekohaseks toimimiseks vajalikud ega saa neid keelata:
- **Seansi küpsised**: Kasutatakse teie sisselogimisseansi ja turvalisuse säilitamiseks
- **Turvaküpsised**: Kasutatakse saitidevaheliste päringuvõltsimise rünnakute vältimiseks

## Analüütikaküpsised
Need küpsised aitavad meil mõista, kuidas külastajad meie veebisaidiga suhtlevad:
- **Google Analytics**: Jälgib lehekülgede vaatamisi, kasutajate käitumist ja veebisaidi jõudlust
- **Jõudluse jälgimine**: Aitab meil tuvastada ja lahendada tehnilisi probleeme

## Turundusküpsised
Neid küpsiseid kasutatakse asjakohase reklaami edastamiseks:
- **Reklaamiplatvormid**: Kasutatakse teile asjakohase reklaami näitamiseks teistes veebisaitides
- **Sotsiaalmeedia integratsioon**: Võimaldab sisu jagamist sotsiaalplatvormidel

## Teie Valikud
Saate küpsiste eelistusi kontrollida meie nõusolekubänneri kaudu või oma brauseri seadete kohandamisega. Pange tähele, et teatud küpsiste keelamine võib mõjutada veebisaidi funktsionaalsust.

Lisateabe saamiseks meie andmete käitlemise kohta vaadake meie Privaatsuspoliitikat.

Viimati uuendatud: ${new Date().toLocaleDateString()}
      `.trim(),
            lastUpdated: new Date()
        });
        this.cookiePolicyCurrentId = 3;
    }
    generateKey(id, lang) {
        return `${id}:${lang}`;
    }
    buildContentKey(section, key, lang) {
        return `${section}:${key}:${lang}`;
    }
    async getAllProducts(language = 'en', category) {
        console.log(`[getAllProducts] 🔍 Filtering products - Language: "${language}", Category: "${category || 'all'}"`);
        // Get all products from storage
        const allProducts = [...this.products.values()];
        // Step 1: Filter by publication status
        const publishedProducts = allProducts.filter(p => {
            const isPublished = p.published !== false; // Include true, undefined, null - exclude only explicit false
            return isPublished;
        });
        // Step 2: Filter by category if specified
        let categoryFilteredProducts = publishedProducts;
        if (category && category !== 'all' && category.trim() !== '') {
            categoryFilteredProducts = publishedProducts.filter(p => {
                const categoryMatch = p.category === category;
                return categoryMatch;
            });
        }
        // Step 3: Filter by language with fallback hierarchy
        const requestedLanguage = language || 'en';
        const languageFilteredProducts = categoryFilteredProducts.filter(p => {
            // If product has no language, include it as a fallback
            if (!p.language) {
                return true;
            }
            // Language priority order:
            // 1. Exact match (e.g., 'en-US' === 'en-US')
            // 2. Language code match (e.g., 'en-US' matches 'en')
            // 3. Default English fallback
            const exactMatch = p.language === requestedLanguage;
            const codeMatch = p.language === requestedLanguage.split('-')[0];
            const englishFallback = p.language === 'en';
            return exactMatch || codeMatch || englishFallback;
        });
        // Final result
        const finalResult = languageFilteredProducts;
        console.log(`[getAllProducts] ✅ Found ${finalResult.length} products (${allProducts.length} total → ${publishedProducts.length} published → ${categoryFilteredProducts.length} category → ${finalResult.length} language)`);
        return finalResult;
    }
    async getProduct(idOrProductId, language = 'en') {
        console.log("🧪 [getProduct] Requested:", { idOrProductId, language });
        const allVersions = [...this.products.values()].filter(p => {
            const match = (p.productId === idOrProductId ||
                (p.id !== undefined && p.id !== null && p.id.toString() === idOrProductId));
            if (match) {
                console.log("✅ Match candidate:", { id: p.id, productId: p.productId, lang: p.language });
            }
            return match;
        });
        if (allVersions.length === 0) {
            console.log("❌ No products found with ID or productId:", idOrProductId);
            return undefined;
        }
        let match = allVersions.find(p => p.language === language);
        if (match)
            return match;
        const shortLang = language.split('-')[0];
        match = allVersions.find(p => p.language === shortLang);
        if (match)
            return match;
        match = allVersions.find(p => p.language === 'en');
        if (match)
            return match;
        return allVersions[0];
    }
    async getProductBySlug(slug, language = 'en') {
        console.log('[getProductBySlug]', slug, language);
        const direct = this.products.get(this.generateKey(slug, language));
        if (direct)
            return direct;
        const all = [...this.products.values()].filter(p => p && typeof p.language === 'string');
        const exact = all.find(p => p.productId === slug && p.language === language);
        if (exact)
            return exact;
        const shortLang = language.split('-')[0];
        const shortLangFallback = all.find(p => p.productId === slug && p.language === shortLang);
        if (shortLangFallback)
            return shortLangFallback;
        const englishFallback = all.find(p => p.productId === slug && p.language === 'en');
        if (englishFallback)
            return englishFallback;
        console.warn(`[getProductBySlug] ❌ No product found for slug: ${slug}`);
        return undefined;
    }
    async getAllProjects(filters) {
        let projects = [...this.projects.values()];
        if (filters) {
            if (filters.published !== undefined) {
                projects = projects.filter(p => p.published === filters.published);
            }
            if (filters.language) {
                projects = projects.filter(p => p.language === filters.language);
            }
            if (filters.productTag) {
                projects = projects.filter(p => p.productTags && p.productTags.includes(filters.productTag));
            }
            if (filters.year) {
                projects = projects.filter(p => p.year === filters.year);
            }
            if (filters.category && filters.category !== 'all') {
                projects = projects.filter(p => p.category === filters.category);
            }
        }
        // Sort by most recent projects first
        return projects.sort((a, b) => {
            // First sort by featured status
            if (a.featured && !b.featured)
                return -1;
            if (!a.featured && b.featured)
                return 1;
            // Then sort by date (desc)
            const dateA = a.projectDate || a.createdAt;
            const dateB = b.projectDate || b.createdAt;
            if (dateA && dateB) {
                return new Date(dateB).getTime() - new Date(dateA).getTime();
            }
            return 0;
        });
    }
    async getProject(id) {
        // Find project by ID across all language versions
        for (const project of this.projects.values()) {
            if (project.id === id) {
                return project;
            }
        }
        return undefined;
    }
    async createProject(projectData) {
        const newId = this.projectCurrentId++;
        const newProject = {
            ...projectData,
            id: newId,
            language: projectData.language || 'en',
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        const key = this.generateKey(newId.toString(), newProject.language);
        this.projects.set(key, newProject);
        return newProject;
    }
    async updateProject(id, updates) {
        // Find the existing project
        const existingProject = await this.getProject(id);
        if (!existingProject) {
            return undefined;
        }
        const updatedProject = {
            ...existingProject,
            ...updates,
            id, // Ensure ID doesn't change
            updatedAt: new Date(),
        };
        // Update the project in storage
        const key = this.generateKey(id.toString(), updatedProject.language);
        this.projects.set(key, updatedProject);
        return updatedProject;
    }
    async deleteProject(id) {
        // Find and delete all language versions of this project
        const keysToDelete = [...this.projects.keys()].filter(k => k.startsWith(`${id}:`));
        if (keysToDelete.length === 0) {
            return false;
        }
        for (const key of keysToDelete) {
            this.projects.delete(key);
        }
        return true;
    }
    async createProduct(product) {
        const key = this.generateKey(product.productId, product.language);
        this.products.set(key, product);
        return product;
    }
    async updateProduct(id, product) {
        // First, check if the product exists
        const existingProduct = await this.getProduct(id, product.language);
        if (!existingProduct) {
            console.error(`[MemStorage.updateProduct] ❌ Product not found: id=${id}, language=${product.language}`);
            return undefined;
        }
        // Create the updated product with proper timestamps
        const updatedProduct = {
            ...existingProduct,
            ...product,
            id: existingProduct.id || existingProduct.productId, // Preserve the ID
            productId: existingProduct.productId, // Preserve the productId
            updatedAt: new Date(),
            createdAt: existingProduct.createdAt || new Date()
        };
        const key = this.generateKey(id, product.language);
        this.products.set(key, updatedProduct);
        console.log(`[MemStorage.updateProduct] ✅ Updated product: ${updatedProduct.title} (${id}:${product.language})`);
        return updatedProduct;
    }
    async deleteProduct(id) {
        const keysToDelete = [...this.products.keys()].filter(k => k.startsWith(`${id}:`));
        for (const key of keysToDelete) {
            this.products.delete(key);
        }
    }
    async getAllPages() {
        return [...this.siteContent.values()].filter(c => c.section === 'page' && c.language === 'en');
    }
    async getPageById(id) {
        const key = this.buildContentKey('page', id, 'en');
        return this.siteContent.get(key);
    }
    async savePageContent(content) {
        const section = content.section ?? 'page';
        const language = content.language ?? 'en';
        const key = this.buildContentKey(section, content.key, language);
        const existing = this.siteContent.get(key);
        if (existing) {
            const updated = { ...existing, ...content, section, language };
            this.siteContent.set(key, updated);
            return updated;
        }
        const newContent = {
            ...content,
            section,
            language,
            id: this.siteContentCurrentId++,
        };
        this.siteContent.set(key, newContent);
        return newContent;
    }
    // Site content management methods
    async getAllSiteContent(language = 'en', section) {
        const allContent = Array.from(this.siteContent.values());
        return allContent.filter(content => {
            const languageMatch = content.language === language;
            const sectionMatch = section ? content.section === section : true;
            return languageMatch && sectionMatch;
        });
    }
    async getSiteContent(id) {
        return Array.from(this.siteContent.values()).find(content => content.id === id);
    }
    async deleteSiteContent(id) {
        const content = await this.getSiteContent(id);
        if (!content)
            return false;
        const key = this.buildContentKey(content.section, content.key, content.language);
        return this.siteContent.delete(key);
    }
    // ===== ADMIN METHODS =====
    // Users management
    async getAllUsers() {
        return Array.from(this.users.values());
    }
    async getUser(id) {
        return this.users.get(id);
    }
    async getUserByUsername(username) {
        return Array.from(this.users.values()).find(u => u.username === username);
    }
    async createUser(userData) {
        const id = this.users.size + 1;
        const user = { ...userData, id, createdAt: new Date() };
        this.users.set(id, user);
        return user;
    }
    async updateUser(id, userData) {
        const user = this.users.get(id);
        if (!user)
            return undefined;
        const updated = { ...user, ...userData, updatedAt: new Date() };
        this.users.set(id, updated);
        return updated;
    }
    async deleteUser(id) {
        return this.users.delete(id);
    }
    async verifyUser(username, password) {
        const user = await this.getUserByUsername(username);
        if (!user)
            return null;
        // Simple password check (in real app, use bcrypt)
        return user.password === password ? user : null;
    }
    async updateUserLastLogin(id) {
        const user = this.users.get(id);
        if (!user)
            return undefined;
        user.lastLogin = new Date();
        return user;
    }
    // Contact Inquiries management
    async getAllContactInquiries() {
        return Array.from(this.contactInquiries.values());
    }
    async getContactInquiry(id) {
        return this.contactInquiries.get(id);
    }
    async createContactInquiry(inquiryData) {
        const id = this.contactInquiries.size + 1;
        const inquiry = { ...inquiryData, id, createdAt: new Date(), status: 'new', archived: false };
        this.contactInquiries.set(id, inquiry);
        return inquiry;
    }
    async updateContactInquiry(id, updates) {
        const inquiry = this.contactInquiries.get(id);
        if (!inquiry)
            return undefined;
        const updated = { ...inquiry, ...updates, updatedAt: new Date() };
        this.contactInquiries.set(id, updated);
        return updated;
    }
    async deleteContactInquiry(id) {
        return this.contactInquiries.delete(id);
    }
    // Quote Requests management
    async getAllQuoteRequests(filters) {
        let requests = Array.from(this.quoteRequests.values());
        if (filters?.status) {
            requests = requests.filter(r => r.status === filters.status);
        }
        if (filters?.archived !== undefined) {
            requests = requests.filter(r => r.archived === filters.archived);
        }
        return requests;
    }
    async createQuoteRequest(requestData) {
        const id = this.quoteRequests.size + 1;
        const request = { ...requestData, id, createdAt: new Date() };
        this.quoteRequests.set(id, request);
        return request;
    }
    async updateQuoteRequestStatus(requestId, status) {
        const request = this.quoteRequests.get(requestId);
        if (!request)
            return undefined;
        request.status = status;
        request.updatedAt = new Date();
        return request;
    }
    async archiveQuoteRequest(requestId, archived) {
        const request = this.quoteRequests.get(requestId);
        if (!request)
            return undefined;
        request.archived = archived;
        request.updatedAt = new Date();
        return request;
    }
    async exportQuoteRequests(format, filters) {
        const requests = await this.getAllQuoteRequests(filters);
        if (format === 'csv') {
            // Simple CSV export
            const headers = 'ID,Customer Name,Email,Status,Created At\n';
            const rows = requests.map(r => `${r.id},"${r.customerName}","${r.customerEmail}","${r.status}","${r.createdAt}"`).join('\n');
            return headers + rows;
        }
        return JSON.stringify(requests, null, 2);
    }
    // CRM management
    async getAllCrmClients(filters) {
        let clients = Array.from(this.crmClients.values());
        if (filters?.name) {
            clients = clients.filter(c => c.name.toLowerCase().includes(filters.name.toLowerCase()));
        }
        if (filters?.company) {
            clients = clients.filter(c => c.company?.toLowerCase().includes(filters.company.toLowerCase()));
        }
        if (filters?.leadStatus) {
            clients = clients.filter(c => c.leadStatus === filters.leadStatus);
        }
        return clients;
    }
    async getCrmClient(id) {
        return this.crmClients.get(id);
    }
    async getCrmClientByEmail(email) {
        return Array.from(this.crmClients.values()).find(c => c.email === email);
    }
    async createCrmClient(clientData) {
        const id = this.crmClients.size + 1;
        const client = { ...clientData, id, createdAt: new Date() };
        this.crmClients.set(id, client);
        return client;
    }
    async updateCrmClient(id, updates) {
        const client = this.crmClients.get(id);
        if (!client)
            return undefined;
        const updated = { ...client, ...updates, updatedAt: new Date() };
        this.crmClients.set(id, updated);
        return updated;
    }
    async deleteCrmClient(id) {
        return this.crmClients.delete(id);
    }
    // Offers management
    async getAllOffers(filters) {
        let offers = Array.from(this.offers.values());
        if (filters?.status) {
            offers = offers.filter(o => o.status === filters.status);
        }
        return offers;
    }
    async getOffer(id) {
        return this.offers.get(id);
    }
    async getOfferByNumber(offerNumber) {
        return Array.from(this.offers.values()).find(o => o.offerNumber === offerNumber);
    }
    async createOffer(offerData) {
        const id = this.offers.size + 1;
        const offer = { ...offerData, id, createdAt: new Date() };
        this.offers.set(id, offer);
        return offer;
    }
    async updateOffer(id, updates) {
        const offer = this.offers.get(id);
        if (!offer)
            return undefined;
        const updated = { ...offer, ...updates, updatedAt: new Date() };
        this.offers.set(id, updated);
        return updated;
    }
    async deleteOffer(id) {
        return this.offers.delete(id);
    }
    async getClientOffers(clientId) {
        return Array.from(this.offers.values()).filter(o => o.clientId === clientId);
    }
    // Documents management
    async getAllDocuments(language, documentType, productCategory, categoryId) {
        let documents = Array.from(this.documents.values());
        if (language) {
            documents = documents.filter(d => d.language === language);
        }
        if (documentType) {
            documents = documents.filter(d => d.documentType === documentType);
        }
        if (productCategory) {
            documents = documents.filter(d => d.productCategory === productCategory);
        }
        if (categoryId) {
            documents = documents.filter(d => d.categoryId === categoryId);
        }
        return documents;
    }
    async getDocument(id) {
        return this.documents.get(id);
    }
    async createDocument(documentData) {
        const id = this.documents.size + 1;
        const document = { ...documentData, id, createdAt: new Date(), downloadCount: 0 };
        this.documents.set(id, document);
        return document;
    }
    async updateDocument(id, updates) {
        const document = this.documents.get(id);
        if (!document)
            return undefined;
        const updated = { ...document, ...updates, updatedAt: new Date() };
        this.documents.set(id, updated);
        return updated;
    }
    async deleteDocument(id) {
        return this.documents.delete(id);
    }
    async incrementDownloadCount(id) {
        const document = this.documents.get(id);
        if (!document)
            return undefined;
        document.downloadCount = (document.downloadCount || 0) + 1;
        return document;
    }
    // Notifications management
    async getUserNotifications(userId, includeRead = false) {
        let notifications = Array.from(this.notifications.values()).filter(n => n.userId === userId);
        if (!includeRead) {
            notifications = notifications.filter(n => !n.isRead);
        }
        return notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    }
    async createNotification(notificationData) {
        const id = this.notifications.size + 1;
        const notification = { ...notificationData, id, createdAt: new Date() };
        this.notifications.set(id, notification);
        return notification;
    }
    async markNotificationAsRead(id) {
        const notification = this.notifications.get(id);
        if (!notification)
            return false;
        notification.isRead = true;
        notification.readAt = new Date();
        return true;
    }
    async markAllNotificationsAsRead(userId) {
        const userNotifications = Array.from(this.notifications.values()).filter(n => n.userId === userId);
        userNotifications.forEach(n => {
            n.isRead = true;
            n.readAt = new Date();
        });
        return true;
    }
    async deleteNotification(id) {
        return this.notifications.delete(id);
    }
    async markNotificationAsUnread(id) {
        const notification = this.notifications.get(id);
        if (!notification)
            return false;
        notification.isRead = false;
        delete notification.readAt;
        return true;
    }
    async clearAllNotifications(userId) {
        const userNotifications = Array.from(this.notifications.values()).filter(n => n.userId === userId);
        userNotifications.forEach(n => {
            this.notifications.delete(n.id);
        });
        return true;
    }
    // Audit logs (for dashboard activity)
    async getAuditLogs(filters) {
        let logs = Array.from(this.auditLogs.values());
        if (filters?.fromDate) {
            logs = logs.filter(l => new Date(l.timestamp) >= filters.fromDate);
        }
        return logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    }
    async getAllDocumentCategories() {
        return Array.from(this.documentCategories.values());
    }
    async createDocumentCategory(data) {
        const id = this.documentCategoryCurrentId++;
        const category = { ...data, id, createdAt: new Date() };
        this.documentCategories.set(id, category);
        return category;
    }
    async getDocumentCategory(id) {
        return this.documentCategories.get(id);
    }
    async updateDocumentCategory(id, data) {
        const category = this.documentCategories.get(id);
        if (!category)
            return undefined;
        const updated = { ...category, ...data, updatedAt: new Date() };
        this.documentCategories.set(id, updated);
        return updated;
    }
    async deleteDocumentCategory(id) {
        return this.documentCategories.delete(id);
    }
    // CRM features
    async getCrmActivityLogs(clientId) { return []; }
    async createCrmActivityLog(data) { return data; }
    async getCrmEmailLogs(clientId) { return []; }
    async createCrmEmailLog(data) { return data; }
    async getAllCrmEmailTemplates() { return []; }
    // ===== JOB POSTINGS METHODS =====
    async getAllJobPostings(language = 'en', publishedOnly = false) {
        console.log(`[getAllJobPostings] 🔍 Filtering job postings - Language: "${language}", Published only: ${publishedOnly}`);
        let jobPostings = [...this.jobPostings.values()];
        // Filter by publication status if requested
        if (publishedOnly) {
            jobPostings = jobPostings.filter(jp => jp.published === true);
        }
        // Apply language fallback hierarchy
        const filteredJobPostings = jobPostings.filter(jp => {
            // Language priority order:
            // 1. Exact match (e.g., 'en-US' === 'en-US')
            // 2. Language code match (e.g., 'en-US' matches 'en')
            // 3. Default English fallback
            const exactMatch = jp.language === language;
            const codeMatch = jp.language === language.split('-')[0];
            const englishFallback = jp.language === 'en';
            return exactMatch || codeMatch || englishFallback;
        });
        // Sort by creation date (newest first)
        const sortedJobPostings = filteredJobPostings.sort((a, b) => {
            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });
        console.log(`[getAllJobPostings] ✅ Found ${sortedJobPostings.length} job postings`);
        return sortedJobPostings;
    }
    async getJobPosting(id) {
        return this.jobPostings.get(id);
    }
    async getJobPostingBySlug(slug, language = 'en') {
        console.log(`[getJobPostingBySlug] 🔍 Looking for slug: "${slug}", language: "${language}"`);
        const allJobPostings = [...this.jobPostings.values()];
        // Find job postings with matching slug
        const matchingJobPostings = allJobPostings.filter(jp => jp.slug === slug);
        if (matchingJobPostings.length === 0) {
            console.log(`[getJobPostingBySlug] ❌ No job posting found with slug: ${slug}`);
            return undefined;
        }
        // Apply language fallback hierarchy
        // 1. Exact match
        let match = matchingJobPostings.find(jp => jp.language === language);
        if (match) {
            console.log(`[getJobPostingBySlug] ✅ Found exact language match`);
            return match;
        }
        // 2. Language code match
        const shortLang = language.split('-')[0];
        match = matchingJobPostings.find(jp => jp.language === shortLang);
        if (match) {
            console.log(`[getJobPostingBySlug] ✅ Found language code match`);
            return match;
        }
        // 3. English fallback
        match = matchingJobPostings.find(jp => jp.language === 'en');
        if (match) {
            console.log(`[getJobPostingBySlug] ✅ Found English fallback`);
            return match;
        }
        // 4. Return first available
        console.log(`[getJobPostingBySlug] ⚠️ Using first available job posting`);
        return matchingJobPostings[0];
    }
    async createJobPosting(jobPostingData) {
        const id = (this.jobPostingCurrentId++).toString();
        const newJobPosting = {
            ...jobPostingData,
            id,
            language: jobPostingData.language || 'en',
            published: jobPostingData.published || false,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.jobPostings.set(id, newJobPosting);
        console.log(`[createJobPosting] ✅ Created job posting with ID: ${id}`);
        return newJobPosting;
    }
    async updateJobPosting(id, updates) {
        const existingJobPosting = this.jobPostings.get(id);
        if (!existingJobPosting) {
            console.log(`[updateJobPosting] ❌ Job posting not found with ID: ${id}`);
            return undefined;
        }
        const updatedJobPosting = {
            ...existingJobPosting,
            ...updates,
            id, // Ensure ID doesn't change
            updatedAt: new Date(),
        };
        this.jobPostings.set(id, updatedJobPosting);
        console.log(`[updateJobPosting] ✅ Updated job posting with ID: ${id}`);
        return updatedJobPosting;
    }
    async deleteJobPosting(id) {
        const deleted = this.jobPostings.delete(id);
        if (deleted) {
            console.log(`[deleteJobPosting] ✅ Deleted job posting with ID: ${id}`);
        }
        else {
            console.log(`[deleteJobPosting] ❌ Job posting not found with ID: ${id}`);
        }
        return deleted;
    }
    // ===== COOKIE CONSENT & GDPR COMPLIANCE METHODS =====
    async storeConsentRecord(consentData) {
        const id = `consent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const record = {
            id,
            ...consentData,
            timestamp: new Date(),
        };
        this.consentRecords.set(id, record);
        console.log(`[storeConsentRecord] ✅ Stored consent record with ID: ${id}`);
        return record;
    }
    async getConsentRecord(id) {
        return this.consentRecords.get(id);
    }
    async getAllConsentRecords(filters) {
        let records = Array.from(this.consentRecords.values());
        if (filters) {
            if (filters.fromDate) {
                records = records.filter(r => r.timestamp >= filters.fromDate);
            }
            if (filters.toDate) {
                records = records.filter(r => r.timestamp <= filters.toDate);
            }
            if (filters.ipAddress) {
                records = records.filter(r => r.ipAddress === filters.ipAddress);
            }
            if (filters.analytics !== undefined) {
                records = records.filter(r => r.consent.analytics === filters.analytics);
            }
            if (filters.marketing !== undefined) {
                records = records.filter(r => r.consent.marketing === filters.marketing);
            }
        }
        return records.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    }
    async deleteConsentRecord(id) {
        const deleted = this.consentRecords.delete(id);
        if (deleted) {
            console.log(`[deleteConsentRecord] ✅ Deleted consent record with ID: ${id}`);
        }
        return deleted;
    }
    // Cookie Policy Content Management
    async getCookiePolicyContent(language = 'en') {
        // Apply language fallback hierarchy
        const allContent = Array.from(this.cookiePolicyContent.values());
        // 1. Exact match
        let match = allContent.find(c => c.language === language);
        if (match)
            return match;
        // 2. Language code match
        const shortLang = language.split('-')[0];
        match = allContent.find(c => c.language === shortLang);
        if (match)
            return match;
        // 3. English fallback
        match = allContent.find(c => c.language === 'en');
        if (match)
            return match;
        // 4. Return first available
        return allContent[0];
    }
    async saveCookiePolicyContent(content) {
        const existing = Array.from(this.cookiePolicyContent.values())
            .find(c => c.language === content.language);
        if (existing) {
            const updated = {
                ...existing,
                ...content,
                lastUpdated: new Date(),
            };
            this.cookiePolicyContent.set(existing.id.toString(), updated);
            console.log(`[saveCookiePolicyContent] ✅ Updated cookie policy for language: ${content.language}`);
            return updated;
        }
        const newContent = {
            ...content,
            id: this.cookiePolicyCurrentId++,
            lastUpdated: new Date(),
        };
        this.cookiePolicyContent.set(newContent.id.toString(), newContent);
        console.log(`[saveCookiePolicyContent] ✅ Created cookie policy for language: ${content.language}`);
        return newContent;
    }
    async getAllCookiePolicyContent() {
        return Array.from(this.cookiePolicyContent.values())
            .sort((a, b) => b.lastUpdated.getTime() - a.lastUpdated.getTime());
    }
    async deleteCookiePolicyContent(id) {
        const deleted = this.cookiePolicyContent.delete(id.toString());
        if (deleted) {
            console.log(`[deleteCookiePolicyContent] ✅ Deleted cookie policy content with ID: ${id}`);
        }
        return deleted;
    }
    // ===== PAGE ANALYTICS METHODS =====
    async getPageStatistics(range) {
        const allPages = Array.from(this.customPages.values());
        const now = new Date();
        let startDate;
        // Calculate date range
        switch (range) {
            case '7d':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case '90d':
                startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                break;
            case '1y':
                startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        }
        // Filter pages by date range
        const pagesInRange = allPages.filter((page) => page.createdAt && page.createdAt >= startDate);
        // Calculate statistics
        const stats = {
            total: allPages.length,
            published: allPages.filter((p) => p.status === 'published').length,
            drafts: allPages.filter((p) => p.status === 'draft').length,
            archived: allPages.filter((p) => p.status === 'archived').length,
            createdInRange: pagesInRange.length,
            views: this.generateMockViews(allPages), // Mock data for demo
            avgViewsPerPage: Math.round(this.generateMockViews(allPages) / Math.max(allPages.length, 1)),
            topPerformers: this.getTopPerformingPages(allPages),
            recentActivity: this.getRecentPageActivity(allPages, 10)
        };
        return stats;
    }
    async getPageAnalytics(range) {
        const allPages = Array.from(this.customPages.values());
        // Generate analytics data for each page
        const analytics = allPages.map((page) => ({
            id: page.id,
            title: page.title,
            slug: page.slug,
            status: page.status,
            language: page.language,
            views: this.generateMockPageViews(page.id), // Mock data for demo
            uniqueVisitors: this.generateMockUniqueVisitors(page.id),
            bounceRate: this.generateMockBounceRate(),
            avgTimeOnPage: this.generateMockTimeOnPage(),
            createdAt: page.createdAt,
            updatedAt: page.updatedAt,
            publishedAt: page.publishedAt
        }));
        // Sort by views descending
        analytics.sort((a, b) => b.views - a.views);
        return analytics;
    }
    async getPageLanguageStats() {
        const allPages = Array.from(this.customPages.values());
        const languageStats = {};
        // Count pages by language
        allPages.forEach((page) => {
            const lang = page.language || 'en';
            languageStats[lang] = (languageStats[lang] || 0) + 1;
        });
        // Convert to array format with percentages
        const total = allPages.length;
        const stats = Object.entries(languageStats).map(([language, count]) => ({
            language,
            count,
            percentage: total > 0 ? Math.round((count / total) * 100) : 0
        }));
        // Sort by count descending
        stats.sort((a, b) => b.count - a.count);
        return {
            total,
            languages: stats,
            distribution: languageStats
        };
    }
    // Helper methods for generating mock analytics data
    generateMockViews(pages) {
        return pages.reduce((total, page) => total + this.generateMockPageViews(page.id), 0);
    }
    generateMockPageViews(pageId) {
        // Generate consistent mock data based on page ID
        const seed = pageId * 123;
        return Math.floor((seed % 1000) + 50);
    }
    generateMockUniqueVisitors(pageId) {
        const views = this.generateMockPageViews(pageId);
        return Math.floor(views * 0.7); // Assume 70% unique visitors
    }
    generateMockBounceRate() {
        return Math.floor(Math.random() * 40) + 30; // 30-70%
    }
    generateMockTimeOnPage() {
        return Math.floor(Math.random() * 180) + 60; // 60-240 seconds
    }
    getTopPerformingPages(pages) {
        return pages
            .map((page) => ({
            id: page.id,
            title: page.title,
            slug: page.slug,
            views: this.generateMockPageViews(page.id),
            status: page.status
        }))
            .sort((a, b) => b.views - a.views)
            .slice(0, 5);
    }
    getRecentPageActivity(pages, limit) {
        return pages
            .filter((page) => page.updatedAt)
            .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
            .slice(0, limit)
            .map((page) => ({
            id: page.id,
            title: page.title,
            action: page.status === 'published' ? 'published' : 'updated',
            timestamp: page.updatedAt,
            status: page.status
        }));
    }
}
