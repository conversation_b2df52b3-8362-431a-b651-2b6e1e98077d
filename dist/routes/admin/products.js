import { Router } from 'express';
import { storage } from '../../storage';
import { insertProductSchema } from '../../shared/schema';
import { z } from 'zod';
import { isAuthenticated } from '../../middleware/adminAuth';
const router = Router();
// Apply authentication middleware to all routes
router.use(isAuthenticated);
// Получить все продукты по языку с правильной иерархией fallback
router.get('/', async (req, res) => {
    try {
        const language = typeof req.query.language === 'string' ? req.query.language : 'en';
        const category = typeof req.query.category === 'string' ? req.query.category : undefined;
        // Use the language fallback hierarchy as specified in requirements
        const products = await storage.getAllProducts(language, category);
        // Добавим поле `id`, если его нет, но есть `productId`
        const patched = products.map((p) => ({
            ...p,
            id: p.id || p.productId,
        }));
        res.status(200).json(patched);
    }
    catch (error) {
        console.error('Error fetching products:', error);
        res.status(500).json({ success: false, message: 'Failed to fetch products' });
    }
});
// Получить один продукт по slug и языку
router.get('/slug/:slug', async (req, res) => {
    try {
        const slug = req.params.slug;
        const language = typeof req.query.language === 'string' ? req.query.language : undefined;
        if (process.env.NODE_ENV !== 'production') {
            console.log(`[Admin Products API] GET /slug/${slug} - Language: ${language}`);
        }
        const product = await storage.getProductBySlug(slug, language);
        if (!product) {
            if (process.env.NODE_ENV !== 'production') {
                console.warn(`[Admin Products API] ❌ Product not found: slug=${slug}, lang=${language}`);
            }
            return res.status(404).json({ success: false, message: 'Product not found' });
        }
        if (process.env.NODE_ENV !== 'production') {
            console.log(`[Admin Products API] ✅ Found product: ${product.title}`);
        }
        res.status(200).json({ success: true, product });
    }
    catch (error) {
        if (process.env.NODE_ENV !== 'production') {
            console.error('[Admin Products API] Failed to fetch product by slug', error);
        }
        res.status(500).json({ success: false, message: 'Failed to fetch product' });
    }
});
// Получить один продукт по id и языку (admin legacy)
router.get('/:id', async (req, res) => {
    try {
        const id = req.params.id;
        const language = typeof req.query.language === 'string' ? req.query.language : undefined;
        if (process.env.NODE_ENV !== 'production') {
            console.log(`[Admin Products API] GET /${id} - Language: ${language}`);
        }
        const product = await storage.getProduct(id, language);
        if (!product) {
            if (process.env.NODE_ENV !== 'production') {
                console.warn(`[Admin Products API] ❌ Product not found: id=${id}, lang=${language}`);
            }
            return res.status(404).json({ success: false, message: 'Product not found' });
        }
        if (process.env.NODE_ENV !== 'production') {
            console.log(`[Admin Products API] ✅ Found product: ${product.title}`);
        }
        res.status(200).json({ success: true, product });
    }
    catch (error) {
        if (process.env.NODE_ENV !== 'production') {
            console.error('[Admin Products API] Failed to fetch product by id', error);
        }
        res.status(500).json({ success: false, message: 'Failed to fetch product' });
    }
});
// Создать продукт
router.post('/', async (req, res) => {
    try {
        const data = insertProductSchema.parse(req.body);
        // Ensure language is set to 'en' if not provided
        const productData = {
            ...data,
            language: data.language || 'en'
        };
        const newProduct = await storage.createProduct(productData);
        res.status(201).json({ success: true, product: newProduct });
    }
    catch (error) {
        if (error instanceof z.ZodError) {
            return res.status(400).json({ success: false, message: error.message });
        }
        res.status(500).json({ success: false, message: 'Failed to create product' });
    }
});
// Обновить продукт (PUT)
router.put('/:id', async (req, res) => {
    try {
        const id = req.params.id;
        const data = insertProductSchema.parse(req.body);
        // Ensure language is set to 'en' if not provided
        const productData = {
            ...data,
            language: data.language || 'en'
        };
        const updated = await storage.updateProduct(id, productData);
        res.status(200).json({ success: true, product: updated });
    }
    catch (error) {
        console.error('Error updating product:', error);
        res.status(500).json({ success: false, message: 'Failed to update product' });
    }
});
// Обновить продукт (PATCH) - для частичных обновлений
router.patch('/:id', async (req, res) => {
    try {
        const id = req.params.id;
        if (process.env.NODE_ENV !== 'production') {
            console.log(`[Admin Products API] PATCH /${id} - Request body:`, JSON.stringify(req.body, null, 2));
        }
        // Get existing product first
        const existingProduct = await storage.getProduct(id);
        if (!existingProduct) {
            if (process.env.NODE_ENV !== 'production') {
                console.error(`[Admin Products API] ❌ Product not found for update: id=${id}`);
            }
            return res.status(404).json({ success: false, message: 'Product not found' });
        }
        if (process.env.NODE_ENV !== 'production') {
            console.log(`[Admin Products API] ✅ Found existing product: ${existingProduct.title}`);
        }
        // Only validate the incoming updates, not the merged data
        const updateFields = { ...req.body };
        // Ensure language is set if provided
        if (updateFields.language) {
            updateFields.language = updateFields.language || 'en';
        }
        // Validate only the update fields that are present
        const fieldsToValidate = {};
        Object.keys(updateFields).forEach(key => {
            if (insertProductSchema.shape[key]) {
                fieldsToValidate[key] = updateFields[key];
            }
        });
        // Validate the update fields
        const validatedUpdates = insertProductSchema.partial().parse(fieldsToValidate);
        // Merge existing data with validated updates
        const updatedData = {
            ...existingProduct,
            ...validatedUpdates,
            language: validatedUpdates.language || existingProduct.language || 'en'
        };
        if (process.env.NODE_ENV !== 'production') {
            console.log(`[Admin Products API] 🔄 Updating product with data:`, JSON.stringify(updatedData, null, 2));
        }
        const updated = await storage.updateProduct(id, updatedData);
        if (!updated) {
            console.error(`[Admin Products API] ❌ Failed to update product: storage.updateProduct returned null/undefined`);
            return res.status(500).json({ success: false, message: 'Failed to update product in storage' });
        }
        if (process.env.NODE_ENV !== 'production') {
            console.log(`[Admin Products API] ✅ Successfully updated product: ${updated.title}`);
        }
        res.status(200).json({ success: true, product: updated });
    }
    catch (error) {
        console.error('Error patching product:', error);
        if (error instanceof z.ZodError) {
            console.error('Validation errors:', error.errors);
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: error.errors
            });
        }
        res.status(500).json({ success: false, message: 'Failed to update product' });
    }
});
// Удалить продукт
router.delete('/:id', async (req, res) => {
    try {
        // Check if product exists first
        const existingProduct = await storage.getProduct(req.params.id);
        if (!existingProduct) {
            return res.status(404).json({ success: false, message: 'Product not found' });
        }
        await storage.deleteProduct(req.params.id);
        res.status(200).json({ success: true });
    }
    catch (error) {
        console.error('Error deleting product:', error);
        res.status(500).json({ success: false, message: 'Failed to delete product' });
    }
});
export default router;
