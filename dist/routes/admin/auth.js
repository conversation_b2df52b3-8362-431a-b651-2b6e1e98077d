import { Router } from 'express';
const router = Router();
function isAuthenticated(req, res, next) {
    if (req.session && req.session.user) {
        return next();
    }
    return res.status(401).json({ success: false, message: 'Unauthorized' });
}
router.post('/login', (req, res) => {
    const { username, password } = req.body;
    if (username === 'admin' && password === 'admin123') {
        // Set session properties for compatibility with both auth formats
        req.session.user = {
            id: 1,
            username: 'admin',
            isAdmin: true
        };
        // Also set direct session properties for middleware compatibility
        req.session.userId = 1;
        req.session.username = 'admin';
        req.session.isAdmin = true;
        // Explicitly save the session for cross-origin requests
        req.session.save((err) => {
            if (err) {
                console.error('[Login] Session save error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Session save failed'
                });
            }
            return res.json({
                success: true,
                user: req.session.user
            });
        });
    }
    else {
        return res.status(401).json({
            success: false,
            message: 'Invalid username or password'
        });
    }
});
router.get('/me', isAuthenticated, (req, res) => {
    res.json({
        success: true,
        user: req.session.user
    });
});
router.post('/logout', (req, res) => {
    req.session.destroy(() => {
        res.json({ success: true });
    });
});
export default router; // ✅ главное, чтобы это было
